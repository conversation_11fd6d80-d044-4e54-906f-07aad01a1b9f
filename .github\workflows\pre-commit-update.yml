name: Pre-commit auto-update

on:
  schedule:
    - cron: "0 3 * * 2"
  # on demand
  workflow_dispatch:

permissions:
  contents: read

jobs:
  auto-update:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false

    - uses: actions/setup-python@v5
      with:
        python-version: "3.12"


    - name: Install pre-commit
      run: pip install pre-commit

    - name: Run auto-update
      run: pre-commit autoupdate

    - uses: peter-evans/create-pull-request@271a8d0340265f705b14b6d32b9829c1cb33d45e # v7.0.8
      with:
        token: ${{ secrets.REPO_SCOPED_TOKEN }}
        add-paths: .pre-commit-config.yaml
        labels: |
          Tech maintenance
          Dependencies
        branch: update/pre-commit-hooks
        title: Update pre-commit hooks
        commit-message: "chore: update pre-commit hooks"
        committer: Freqtrade <PERSON><PERSON> <<EMAIL>>
        author: <PERSON>e<PERSON><PERSON><PERSON> <PERSON> <<EMAIL>>
        body: Update versions of pre-commit hooks to latest version.
        delete-branch: true
