# Sources and References

This document contains all the original sources and references used in the Comprehensive Trading Strategies Guide.

## Primary Source

All content in this guide is derived from the comprehensive trading tutorial series at **Dot Net Tutorials**.

**Main Website:** [https://dotnettutorials.net/](https://dotnettutorials.net/)

## Article Sources by Category

### Foundation Concepts

#### Candlestick Analysis
- [How to Study Candlestick in Trading](https://dotnettutorials.net/lesson/how-to-study-candlestick/)
- [Candlestick Analysis in Trading](https://dotnettutorials.net/lesson/candlestick-analysis-trading/)

#### Price Action Analysis
- [Price Action Analysis in Trading](https://dotnettutorials.net/lesson/price-action-analysis/)
- [Advanced Price Action Analysis in Trading](https://dotnettutorials.net/lesson/price-action-analysis-trading/)

#### Market Structure and Trends
- [Thrust Pullback and Measuring Move Analysis](https://dotnettutorials.net/lesson/thrust-pullback-measuring-move-analysis/)

#### Volume Analysis
- [Volume Spike Trading Strategy](https://dotnettutorials.net/lesson/volume-spike-trading-strategy/)

### Technical Analysis Tools

#### Fibonacci Strategies
- [Fibonacci Trading Strategy](https://dotnettutorials.net/lesson/fibonacci-trading-strategy/)
- [Fibonacci Trading Strategy using Confluence Factor](https://dotnettutorials.net/lesson/fibonacci-trading-strategy-using-confluence-factor/)

### Advanced Strategies

#### Smart Money Trading
- [How to Trade with Smart Money](https://dotnettutorials.net/lesson/how-to-trade-with-smart-money/)
- [Order Block Trading Strategy](https://dotnettutorials.net/lesson/order-block-trading-strategy/)

#### Volume-Based Strategies
- [Volume Spike Trading Strategy](https://dotnettutorials.net/lesson/volume-spike-trading-strategy/)

### Specialized Strategies

#### Pattern-Based Trading
- [Volatility Contraction Pattern Strategy](https://dotnettutorials.net/lesson/volatility-contraction-pattern-strategy/)

## Planned Sources (To Be Processed)

The following articles from the Dot Net Tutorials series are planned for inclusion in future updates:

### Day Trading Strategies
- VWAP Trading Strategies
- Gap Trading Techniques
- Open High Open Low Strategy
- PIN BAR Trading Methods

### Chart Patterns
- Head and Shoulders Patterns
- Wide Range Bar Trading
- Essential Candlestick Patterns

### Additional Technical Tools
- Trendline Trading Strategies
- Multiple Timeframe Analysis
- Support and Resistance Analysis

### More Advanced Strategies
- Supply and Demand Zone Trading
- Pullback Trading Strategies
- Breakout Trading Methods
- Sideways Price Action Trading

## Image References

### Candlestick Analysis
- [What is a Candlestick?](https://dotnettutorials.net/wp-content/uploads/2019/12/what-is-a-candlestick.png)
- [Bullish Candlestick](https://dotnettutorials.net/wp-content/uploads/2019/12/bullish-candlestick-1.png)
- [Candlestick Analysis in Trading](https://dotnettutorials.net/wp-content/uploads/2020/04/word-image-122.png)
- [Understanding Candlestick Analysis](https://dotnettutorials.net/wp-content/uploads/2020/04/word-image-123.png)

### Price Action Analysis
- [The size of the body (high to low)](https://dotnettutorials.net/wp-content/uploads/2019/12/word-image-23.png)
- [STEP 2: The length of wicks](https://dotnettutorials.net/wp-content/uploads/2019/12/word-image-24.png)
- [Advanced Price Action Analysis](https://dotnettutorials.net/wp-content/uploads/2020/01/word-image-197.png)
- [Advanced Price Action Analysis](https://dotnettutorials.net/wp-content/uploads/2020/01/word-image-198.png)

### Market Structure Analysis
- [Thrust, pullback and Measuring move Analysis](https://dotnettutorials.net/wp-content/uploads/2020/02/word-image-30.png)
- [Thrust, pullback and Measuring move Analysis](https://dotnettutorials.net/wp-content/uploads/2020/02/word-image-31.png)

### Smart Money Trading
- [How to trade with Smart Money](https://dotnettutorials.net/wp-content/uploads/2019/12/word-image-49.png)

### Fibonacci Trading
- [Fibonacci vs Moving Average Comparison](https://dotnettutorials.net/wp-content/uploads/2023/02/word-image-34712-1.png)
- [Fibonacci Trading Strategy Examples](https://dotnettutorials.net/wp-content/uploads/2023/02/word-image-34712-2.png)
- [Fibonacci Confluence Strategy](https://dotnettutorials.net/wp-content/uploads/2023/02/word-image-34984-1.png)
- [Market Trend Principles](https://dotnettutorials.net/wp-content/uploads/2023/02/word-image-34984-2.png)

### Volume Spike Trading
- [Volume Spike Examples](https://dotnettutorials.net/wp-content/uploads/2023/01/word-image-34444-1.png)
- [Volume Spike Trading Examples](https://dotnettutorials.net/wp-content/uploads/2023/01/word-image-34444-2.png)

### Order Block Trading
- [Order Block Trading Examples](https://dotnettutorials.net/wp-content/uploads/2023/02/word-image-35367-1.png)
- [Order Block Price Action](https://dotnettutorials.net/wp-content/uploads/2023/02/word-image-35367-2.png)

### Volatility Patterns
- [Volatility Contraction Pattern](https://dotnettutorials.net/wp-content/uploads/2023/01/word-image-34443-1.png)
- [Volatility Contraction Pattern](https://dotnettutorials.net/wp-content/uploads/2023/01/word-image-34443-2.png)

## Additional Resources

### Related Trading Concepts
While our primary focus is on the Dot Net Tutorials series, these concepts are also covered in various trading education resources:

- **Wyckoff Method** - Volume and price analysis principles
- **Smart Money Concepts** - Institutional trading behavior
- **Market Structure Analysis** - Trend and swing analysis
- **Technical Analysis** - Chart patterns and indicators

### Recommended Reading Order
For beginners to trading, we recommend following this sequence:

1. **Start with Foundations** - Candlestick and Price Action Analysis
2. **Learn Market Structure** - Understanding trends and swings
3. **Master Technical Tools** - Fibonacci and trendline analysis
4. **Practice Day Trading** - VWAP and gap strategies
5. **Advance to Smart Money** - Order blocks and institutional concepts

## Content Attribution

All content in this guide is:
- **Summarized and organized** from the original Dot Net Tutorials articles
- **Restructured for learning** with logical progression
- **Enhanced with cross-references** between related concepts
- **Formatted for easy navigation** and reference

**Original Content Credit:** Dot Net Tutorials (dotnettutorials.net)
**Organization and Summary:** This comprehensive guide compilation

## Updates and Maintenance

This guide is continuously updated as we process more articles from the Dot Net Tutorials trading series. Each update includes:

- New strategy summaries
- Enhanced cross-referencing
- Improved organization
- Additional examples and clarifications

**Last Updated:** [Current Date]
**Next Planned Update:** Processing remaining day trading and chart pattern articles

---

*For the complete and original content, always refer to the source articles at [Dot Net Tutorials](https://dotnettutorials.net/)*
