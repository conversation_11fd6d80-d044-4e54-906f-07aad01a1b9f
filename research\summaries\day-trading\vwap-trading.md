# VWAP Trading Strategy

VWAP (Volume Weighted Average Price) is a powerful intraday trading tool used by institutional traders and smart money to identify optimal entry and exit points. This comprehensive guide covers VWAP trading strategies for day traders.

## Table of Contents

- [What is VWAP?](#what-is-vwap)
- [Uses of VWAP](#uses-of-vwap)
- [VWAP as Support and Resistance](#vwap-as-support-and-resistance)
- [Determining Market Trend with VWAP](#determining-market-trend-with-vwap)
- [VWAP Trading Strategies](#vwap-trading-strategies)
- [VWAP Reversal Strategy](#vwap-reversal-strategy)
- [VWAP False Breakout Strategy](#vwap-false-breakout-strategy)
- [VWAP First Pullback Strategy](#vwap-first-pullback-strategy)
- [Limitations and Risk Management](#limitations-and-risk-management)
- [Related Concepts](#related-concepts)

## What is VWAP?

### Definition

VWAP stands for **Volume Weighted Average Price**. It's a trading benchmark that calculates the average price of a security based on both price and volume, giving more weight to periods with higher volume.

### Institutional Use

**Smart Money Application:**
- **Professional traders** use VWAP to measure trading performance
- **Investment banks and hedge funds** compare their execution prices to VWAP
- **Buy orders below VWAP** = good fill (below-average price)
- **Sell orders above VWAP** = good fill (above-average price)

**Algorithmic Trading:**
- **VWAP algorithms** help institutions execute large orders
- **Minimize market impact** by trading close to VWAP
- **Benchmark performance** against VWAP values

### Key Characteristics

**Time Independence:**
- **Same value** across all timeframes
- **Consistent reference** throughout trading day
- **Cumulative calculation** from market open

## Uses of VWAP

### 1. Control Indicator

**Market Control Analysis:**
- **Price above VWAP** = Buyers in control, buying demand present
- **Price below VWAP** = Sellers in control, selling pressure present
- **VWAP direction** indicates overall market sentiment

**VWAP Direction Signals:**
- **Rising VWAP** = Buyers in control
- **Falling VWAP** = Sellers in control
- **Flat VWAP** = No clear control, range-bound market

### 2. Support and Resistance

**Dynamic Level:**
- **VWAP acts as dynamic support/resistance** throughout the day
- **Smart money buys below VWAP** and sells above it
- **Large orders often executed** near VWAP levels

### 3. Trend Determination

**Market Bias Identification:**
- **Bullish trend days** = Market stays above VWAP
- **Bearish trend days** = Market stays below VWAP
- **Ranging sessions** = Market oscillates around flat VWAP

## VWAP as Support and Resistance

### Support Characteristics

**VWAP Support Signals:**
- **Price unable to close below VWAP**
- **Rejection from VWAP** with long lower wicks
- **Volume increase** on approach to VWAP
- **Engulfing patterns** or outside bars at VWAP

### Resistance Characteristics

**VWAP Resistance Signals:**
- **Price unable to close above VWAP**
- **Rejection from VWAP** with long upper wicks
- **Volume increase** on approach to VWAP
- **Reversal patterns** at VWAP level

### Confirmation Requirements

**Price Action Confirmation:**
- **Volume must confirm** support/resistance
- **Candlestick patterns** at VWAP levels
- **Multiple touches** strengthen the level
- **Clean breaks** with volume indicate level failure

## Determining Market Trend with VWAP

### Opening Considerations

**Avoid Early Trading:**
- **First 5-10 minutes** show high volatility
- **Overnight positions** and institutional orders create noise
- **Wait 10-15 minutes** for volatility to decrease
- **Let market establish** relationship with VWAP

### Trending Session Identification

**Step-by-Step Process:**

#### Step 1: Initial Direction
- **Look for 2-3 candles** in same direction
- **Establish initial bias** from market open
- **Note volume characteristics** during initial move

#### Step 2: VWAP Test
- **When price first approaches VWAP**, look for push away
- **Price should not stall** at VWAP level
- **Strong rejection** indicates trending potential

#### Step 3: Follow-Through Analysis
- **Observe if push enjoys follow-through** or gets rejected
- **Rejection back to VWAP** = ranging session likely
- **Strong follow-through** = trending session confirmed

#### Step 4: Trade Direction
- **Trending session** = Take momentum trades in trend direction
- **Ranging session** = Take mean-reversion trades

### Ranging Session Characteristics

**Range-Bound Market Signals:**
- **Price frequently near VWAP**
- **Multiple VWAP crossings**
- **Price stays in opening range**
- **Inside and outside bars** near each other
- **High and low of day** hold throughout session

**James Dalton Quote:** "Markets trend only 20 to 30 percent of the time"

## VWAP Trading Strategies

### Basic Trading Principles

**PVWAP vs VWAP:**
- **PVWAP** = Previous day's VWAP (horizontal line)
- **VWAP** = Current day's VWAP (dynamic line)

**Core Rules:**
- **Don't go long** below VWAP
- **Don't go short** above VWAP
- **Extended from VWAP** = reversal play okay, target VWAP
- **Above PVWAP** = bullish bias, look for buy entries
- **Below PVWAP** = bearish bias, look for sell entries

### Entry Criteria

**Buy Entry Requirements:**
- **5-minute candle completely above** both VWAP and PVWAP
- **Entry above high** of that candle
- **Volume confirmation** preferred

**Sell Entry Requirements:**
- **5-minute candle completely below** both VWAP and PVWAP
- **Entry below low** of that candle
- **Volume confirmation** preferred

**Note:** Strategy doesn't work on range-bound days

## VWAP Reversal Strategy

### Uptrend Reversal Setup

**Requirements:**
- **Previous day trend UP** (higher highs, higher lows)
- **Price should not close** below last swing low
- **Price should close above VWAP**
- **Look for weakness** in morning move into PVWAP level
- **Faster move preferred** with low volume
- **Current day price** should not close below PVWAP

### Entry Process

**Step-by-Step Execution:**

#### Step 1: Trend Identification
- **Find stock in clear trend** (HH/HL for uptrend, LH/LL for downtrend)
- **Confirm trend structure** on higher timeframe

#### Step 2: Retracement Analysis
- **Weak retracement move** towards PVWAP
- **Low volume** during retracement preferred
- **Price stalls** at PVWAP level

#### Step 3: VWAP Test
- **When price first approaches VWAP**, look for push away
- **Price should not stall** at VWAP
- **Strong rejection** from VWAP level

#### Step 4: Follow-Through
- **Observe follow-through** or rejection
- **Strong follow-through** = trend continuation
- **Rejection back to VWAP** = potential reversal

### Risk Management

**Entry Criteria:**
- **Good risk/reward ratio** required
- **Clear stop loss** below PVWAP
- **Target** previous swing high/low

## VWAP False Breakout Strategy

### Concept

**Market Psychology:**
- **Strong stocks stay above VWAP** with institutional buying
- **Weak stocks fall back below VWAP** without institutional support
- **False breakouts** create trading opportunities

### Bullish False Breakout

**Setup:**
- **Stock below VWAP** bounces and breaks above
- **Buyers gaining control**, shorts covering
- **Momentum trade** opportunity to "squeeze shorts"

### Bearish False Breakout

**Setup:**
- **Stock above VWAP** fails and drops below
- **Sellers gaining control**, longs exiting
- **Momentum trade** opportunity following institutional selling

### Confirmation Requirements

**Price Action and Volume:**
- **Volume spike** on breakout
- **Follow-through** in breakout direction
- **No immediate return** to VWAP level

## VWAP First Pullback Strategy

### Setup Requirements

**Trend Confirmation:**
- **Clear trend** established (HH/HL or LH/LL)
- **2-3 candles** in same direction
- **Strong move away** from VWAP initially

### Entry Process

#### Step 1: Trend Establishment
- **Identify clear trend** direction
- **Confirm with multiple candles** in same direction

#### Step 2: VWAP Approach
- **First-time price approaches VWAP**
- **Look for push away** from VWAP
- **Price should not stall** at VWAP level

#### Step 3: Rejection Analysis
- **Look for rejection** from VWAP level
- **Volume confirmation** of rejection
- **Candlestick patterns** supporting rejection

#### Step 4: Follow-Through
- **Observe follow-through** from rejection
- **Strong follow-through** = trend continuation
- **Weak follow-through** = potential reversal

### Risk Management

**Stop Loss Placement:**
- **Below VWAP** for long trades
- **Above VWAP** for short trades
- **Beyond rejection candle** for tighter stops

## Limitations and Risk Management

### VWAP Limitations

1. **Lagging Indicator**
   - **Lag increases** as day progresses
   - **Less reliable** in late trading hours

2. **Opening Restrictions**
   - **Cannot be used** at market opening
   - **High volatility** makes VWAP unreliable early

3. **Supporting Analysis Required**
   - **Requires price action** confirmation
   - **Volume analysis** essential
   - **Not standalone** strategy

### Risk Management Rules

**Position Sizing:**
- **Smaller positions** on range-bound days
- **Larger positions** on trending days
- **Risk based on** distance to stop loss

**Stop Loss Guidelines:**
- **Respect VWAP levels** for stops
- **Use price action** for stop placement
- **Time-based stops** if no movement

**Profit Taking:**
- **Scale out** at resistance levels
- **Trail stops** in trending moves
- **Target VWAP** on reversal trades

### Success Rate

**Strategy Performance:**
- **60-80% win rate** with proper execution
- **Not a holy grail** strategy
- **Practice required** before live trading
- **Market conditions** affect success rate

## Related Concepts

- [Volume Analysis](../foundations/volume-analysis.md) - Understanding volume patterns
- [Support and Resistance](../foundations/support-resistance.md) - Key level identification
- [Price Action Analysis](../foundations/price-action-analysis.md) - Reading price behavior
- [Gap Trading](gap-trading.md) - Related intraday strategies
- [Smart Money Trading](../advanced-strategies/smart-money-trading.md) - Institutional trading concepts

---

*For more intraday strategies, see [Gap Trading](gap-trading.md) and [Open High Open Low Strategy](open-high-low.md)*
