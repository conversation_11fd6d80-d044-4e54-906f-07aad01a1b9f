# Support and Resistance

Support and resistance levels are fundamental concepts in technical analysis that represent key price levels where buying and selling pressure creates significant market reactions. Understanding these levels is crucial for identifying trading opportunities and managing risk.

## Table of Contents

- [What are Support and Resistance?](#what-are-support-and-resistance)
- [Types of Support and Resistance](#types-of-support-and-resistance)
- [Identifying Key Levels](#identifying-key-levels)
- [Volume Spread Analysis](#volume-spread-analysis)
- [Trading Support and Resistance](#trading-support-and-resistance)
- [Advanced Concepts](#advanced-concepts)
- [Confirmation and Disconfirmation](#confirmation-and-disconfirmation)
- [Risk Management](#risk-management)
- [Related Concepts](#related-concepts)

## What are Support and Resistance?

### Support

**Definition:** A price level where buying (demand) is sufficient to halt a downtrend and possibly reverse it.

**Characteristics:**
- Price tends to bounce higher from support levels
- Represents a floor where buyers step in
- Multiple touches strengthen the support level
- Breaking support often leads to further decline

### Resistance

**Definition:** A price level where selling (supply) is sufficient to halt an uptrend and possibly reverse it.

**Characteristics:**
- Price tends to reverse lower from resistance levels
- Represents a ceiling where sellers step in
- Multiple touches strengthen the resistance level
- Breaking resistance often leads to further advance

### Why Support and Resistance Work

**Market Psychology:**
- **Memory effect:** Traders remember significant price levels
- **Round numbers:** Psychological significance of even numbers
- **Previous turning points:** Historical significance creates future relevance
- **Order clustering:** Stop losses and limit orders cluster around key levels

## Types of Support and Resistance

### Horizontal Support and Resistance

**Static Levels:**
- **Previous swing highs and lows**
- **Round numbers** (100, 1000, 10000, etc.)
- **Historical turning points**
- **Opening and closing prices** of significant periods

**Characteristics:**
- Remain constant over time
- Easy to identify on charts
- Often provide strong reactions
- Multiple timeframe significance

### Dynamic Support and Resistance

**Moving Levels:**
- **Trendlines** - diagonal support/resistance
- **Moving averages** - dynamic levels that change with price
- **Fibonacci levels** - retracement and extension levels
- **Bollinger Bands** - volatility-based levels

**Characteristics:**
- Change position over time
- Follow price movement
- Provide ongoing reference points
- Require regular adjustment

### Psychological Levels

**Round Numbers:**
- **Major round numbers:** 100, 1000, 10000
- **Minor round numbers:** 50, 150, 250
- **Currency pairs:** 1.0000, 1.5000, 2.0000
- **Percentage levels:** 25%, 50%, 75%

**Why They Work:**
- Easy to remember and reference
- Natural human tendency to use round numbers
- Order clustering around these levels
- Media and analyst focus on round numbers

## Identifying Key Levels

### Historical Analysis

**Process:**
1. **Scan charts** for obvious turning points
2. **Mark significant highs and lows**
3. **Identify levels with multiple touches**
4. **Note volume characteristics** at these levels

### Level Strength Factors

**Factors That Strengthen Levels:**

1. **Number of Touches**
   - More touches = stronger level
   - Each successful test increases significance

2. **Timeframe**
   - Higher timeframe levels are stronger
   - Daily levels stronger than hourly
   - Weekly levels stronger than daily

3. **Volume**
   - High volume at level increases significance
   - Volume spikes show institutional interest

4. **Recency**
   - Recent levels often more relevant
   - Fresh in traders' memory

5. **Round Numbers**
   - Psychological significance
   - Natural clustering of orders

### Multiple Timeframe Analysis

**Approach:**
- **Identify levels** on higher timeframes first
- **Refine entry/exit** on lower timeframes
- **Prioritize** higher timeframe levels
- **Look for confluence** between timeframes

## Volume Spread Analysis

### Finding Support and Resistance with Volume

**Risk-to-Reward Advantage:**
- Trading from support or resistance levels favors risk-to-reward ratios
- Tight stops possible near key levels
- Clear invalidation points

### Volume Patterns at Key Levels

**Support Characteristics:**
- **High volume on approach** to support
- **Buying interest** emerges at level
- **Volume spike** on bounce from support
- **Decreasing volume** on pullbacks

**Resistance Characteristics:**
- **High volume on approach** to resistance
- **Selling interest** emerges at level
- **Volume spike** on rejection from resistance
- **Decreasing volume** on rallies

### Volume Spread Analysis Signals

**Bullish Signals at Support:**
- **Wide spread down bar** with high volume followed by narrow spread
- **Hammer or pin bar** with high volume
- **Volume climax** followed by price recovery
- **Absorption** - high volume with little price movement

**Bearish Signals at Resistance:**
- **Wide spread up bar** with high volume followed by narrow spread
- **Shooting star or pin bar** with high volume
- **Volume climax** followed by price decline
- **Distribution** - high volume with little upward progress

## Trading Support and Resistance

### Support Trading Strategies

#### Bounce Strategy

**Setup:**
- Price approaches established support level
- Look for reversal signals (pin bars, hammers)
- Volume confirmation preferred

**Entry:**
- **Aggressive:** At support level with reversal signal
- **Conservative:** On break above previous high after bounce

**Stop Loss:** Below support level

**Target:** Previous resistance or measured move

#### Support Break Strategy

**Setup:**
- Price breaks below established support
- Volume confirmation of break
- Retest of broken support as resistance

**Entry:**
- On break below support
- On retest of broken support (now resistance)

**Stop Loss:** Above broken support level

**Target:** Next support level or measured move

### Resistance Trading Strategies

#### Rejection Strategy

**Setup:**
- Price approaches established resistance level
- Look for reversal signals (shooting stars, pin bars)
- Volume confirmation preferred

**Entry:**
- **Aggressive:** At resistance level with reversal signal
- **Conservative:** On break below previous low after rejection

**Stop Loss:** Above resistance level

**Target:** Previous support or measured move

#### Resistance Break Strategy

**Setup:**
- Price breaks above established resistance
- Volume confirmation of break
- Retest of broken resistance as support

**Entry:**
- On break above resistance
- On retest of broken resistance (now support)

**Stop Loss:** Below broken resistance level

**Target:** Next resistance level or measured move

## Advanced Concepts

### Role Reversal

**Principle:** Broken support becomes resistance; broken resistance becomes support

**Mechanism:**
- **Previous buyers** at support become sellers when price returns
- **Previous sellers** at resistance become buyers when price returns
- **Psychological impact** of broken levels

**Trading Application:**
- **Retest trades:** Enter when price retests broken level
- **Confirmation:** Look for rejection at the new role
- **Stop placement:** Beyond the broken level

### Zone Concept

**Support/Resistance Zones:**
- **Price ranges** rather than exact levels
- **More realistic** than precise lines
- **Account for market noise** and spread

**Zone Identification:**
- **Cluster of turning points** in small price range
- **Round number areas** (e.g., 1.2950-1.3050)
- **Fibonacci confluence** zones

### False Breaks

**Definition:** Price briefly breaks support/resistance then quickly reverses

**Characteristics:**
- **Low volume** on break
- **Quick reversal** back inside range
- **Stop hunting** behavior

**Trading False Breaks:**
- **Fade the break** if volume is low
- **Wait for confirmation** before trading breaks
- **Use wider stops** to avoid false break stop-outs

## Confirmation and Disconfirmation

### Confirming Support/Resistance

**Confirmation Signals:**
- **Multiple touches** without breaking
- **Volume increase** on approach to level
- **Reversal candlestick patterns** at level
- **Momentum divergence** at level

### Disconfirming Support/Resistance

**Disconfirmation Signals:**
- **Clean break** with volume
- **No immediate return** to broken level
- **Follow-through** in break direction
- **Time spent** beyond the level

**Warning Signs:**
- **Tight ranges** and frequent tests weaken levels
- **Decreasing volume** on approaches
- **Smaller reactions** at each test
- **Time decay** - old levels lose relevance

## Risk Management

### Stop Loss Placement

**Support Trading:**
- **Tight stops:** Just below support level
- **Wider stops:** Below significant low beyond support
- **Time stops:** Exit if no bounce within expected timeframe

**Resistance Trading:**
- **Tight stops:** Just above resistance level
- **Wider stops:** Above significant high beyond resistance
- **Time stops:** Exit if no rejection within expected timeframe

### Position Sizing

**Factors to Consider:**
- **Distance to stop loss**
- **Strength of support/resistance level**
- **Market volatility**
- **Risk tolerance**

**Guidelines:**
- **Smaller positions** at weaker levels
- **Larger positions** at strong, well-tested levels
- **Scale in/out** at major levels

### Risk-Reward Optimization

**Favorable Setups:**
- **Strong levels** with tight stops
- **Clear targets** at next major level
- **Multiple timeframe** confirmation
- **Volume confirmation**

## Related Concepts

- [Market Structure](market-structure.md) - Understanding overall market framework
- [Price Action Analysis](price-action-analysis.md) - Reading price behavior at key levels
- [Volume Analysis](volume-analysis.md) - Confirming support/resistance with volume
- [Fibonacci Strategies](../technical-tools/fibonacci-strategies.md) - Dynamic support/resistance levels
- [Breakout Trading](../specialized/breakout-trading.md) - Trading support/resistance breaks

---

*For specific support/resistance patterns, see [Supply and Demand Zones](../advanced-strategies/supply-demand-zones.md)*
