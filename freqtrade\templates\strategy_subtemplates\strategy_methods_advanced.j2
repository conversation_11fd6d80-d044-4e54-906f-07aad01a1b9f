
def bot_loop_start(self, current_time: datetime, **kwargs) -> None:
    """
    Called at the start of the bot iteration (one loop).
    Might be used to perform pair-independent tasks
    (e.g. gather some remote resource for comparison)

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, this simply does nothing.
    :param current_time: datetime object, containing the current datetime
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    """
    pass

def custom_entry_price(
    self,
    pair: str,
    trade: Trade | None,
    current_time: datetime,
    proposed_rate: float,
    entry_tag: str | None,
    side: str,
    **kwargs,
) -> float:
    """
    Custom entry price logic, returning the new entry price.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, returns None, orderbook is used to set entry price

    :param pair: Pair that's currently analyzed
    :param trade: trade object (None for initial entries).
    :param current_time: datetime object, containing the current datetime
    :param proposed_rate: Rate, calculated based on pricing settings in exit_pricing.
    :param entry_tag: Optional entry_tag (buy_tag) if provided with the buy signal.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return float: New entry price value if provided
    """
    return proposed_rate

def adjust_order_price(
    self,
    trade: Trade,
    order: Order | None,
    pair: str,
    current_time: datetime,
    proposed_rate: float,
    current_order_rate: float,
    entry_tag: str | None,
    side: str,
    is_entry: bool,
    **kwargs,
) -> float | None:
    """
    Exit and entry order price re-adjustment logic, returning the user desired limit price.
    This only executes when a order was already placed, still open (unfilled fully or partially)
    and not timed out on subsequent candles after entry trigger.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-callbacks/

    When not implemented by a strategy, returns current_order_rate as default.
    If current_order_rate is returned then the existing order is maintained.
    If None is returned then order gets canceled but not replaced by a new one.

    :param pair: Pair that's currently analyzed
    :param trade: Trade object.
    :param order: Order object
    :param current_time: datetime object, containing the current datetime
    :param proposed_rate: Rate, calculated based on pricing settings in entry_pricing.
    :param current_order_rate: Rate of the existing order in place.
    :param entry_tag: Optional entry_tag (buy_tag) if provided with the buy signal.
    :param side: 'long' or 'short' - indicating the direction of the proposed trade
    :param is_entry: True if the order is an entry order, False if it's an exit order.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return float or None: New entry price value if provided
    """
    return current_order_rate

def custom_exit_price(
    self,
    pair: str,
    trade: Trade,
    current_time: datetime,
    proposed_rate: float,
    current_profit: float,
    exit_tag: str | None,
    **kwargs,
) -> float:
    """
    Custom exit price logic, returning the new exit price.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, returns None, orderbook is used to set exit price

    :param pair: Pair that's currently analyzed
    :param trade: trade object.
    :param current_time: datetime object, containing the current datetime
    :param proposed_rate: Rate, calculated based on pricing settings in exit_pricing.
    :param current_profit: Current profit (as ratio), calculated based on current_rate.
    :param exit_tag: Exit reason.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return float: New exit price value if provided
    """
    return proposed_rate

def custom_stake_amount(
    self,
    pair: str,
    current_time: datetime,
    current_rate: float,
    proposed_stake: float,
    min_stake: float | None,
    max_stake: float,
    leverage: float,
    entry_tag: str | None,
    side: str,
    **kwargs,
) -> float:
    """
    Customize stake size for each new trade.

    :param pair: Pair that's currently analyzed
    :param current_time: datetime object, containing the current datetime
    :param current_rate: Rate, calculated based on pricing settings in exit_pricing.
    :param proposed_stake: A stake amount proposed by the bot.
    :param min_stake: Minimal stake size allowed by exchange.
    :param max_stake: Balance available for trading.
    :param leverage: Leverage selected for this trade.
    :param entry_tag: Optional entry_tag (buy_tag) if provided with the buy signal.
    :param side: 'long' or 'short' - indicating the direction of the proposed trade
    :return: A stake size, which is between min_stake and max_stake.
    """
    return proposed_stake

use_custom_roi = True

def custom_roi(
    self,
    pair: str,
    trade: Trade,
    current_time: datetime,
    trade_duration: int,
    entry_tag: str | None,
    side: str,
    **kwargs,
) -> float | None:
    """
    Custom ROI logic, returns a new minimum ROI threshold (as a ratio, e.g., 0.05 for +5%).
    Only called when use_custom_roi is set to True.

    If used at the same time as minimal_roi, an exit will be triggered when the lower
    threshold is reached. Example: If minimal_roi = {"0": 0.01} and custom_roi returns 0.05,
    an exit will be triggered if profit reaches 5%.

    :param pair: Pair that's currently analyzed.
    :param trade: trade object.
    :param current_time: datetime object, containing the current datetime.
    :param trade_duration: Current trade duration in minutes.
    :param entry_tag: Optional entry_tag (buy_tag) if provided with the buy signal.
    :param side: 'long' or 'short' - indicating the direction of the current trade.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return float: New ROI value as a ratio, or None to fall back to minimal_roi logic.
    """
    return None

use_custom_stoploss = True

def custom_stoploss(
    self,
    pair: str,
    trade: Trade,
    current_time: datetime,
    current_rate: float,
    current_profit: float,
    after_fill: bool,
    **kwargs,
) -> float | None:
    """
    Custom stoploss logic, returning the new distance relative to current_rate (as ratio).
    e.g. returning -0.05 would create a stoploss 5% below current_rate.
    The custom stoploss can never be below self.stoploss, which serves as a hard maximum loss.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, returns the initial stoploss value.
    Only called when use_custom_stoploss is set to True.

    :param pair: Pair that's currently analyzed
    :param trade: trade object.
    :param current_time: datetime object, containing the current datetime
    :param current_rate: Rate, calculated based on pricing settings in exit_pricing.
    :param current_profit: Current profit (as ratio), calculated based on current_rate.
    :param after_fill: True if the stoploss is called after the order was filled.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return float: New stoploss value, relative to the current_rate
    """

def custom_exit(
    self,
    pair: str,
    trade: Trade,
    current_time: datetime,
    current_rate: float,
    current_profit: float,
    **kwargs,
) -> str | bool | None:
    """
    Custom exit signal logic indicating that specified position should be sold. Returning a
    string or True from this method is equal to setting sell signal on a candle at specified
    time. This method is not called when sell signal is set.

    This method should be overridden to create sell signals that depend on trade parameters. For
    example you could implement a sell relative to the candle when the trade was opened,
    or a custom 1:2 risk-reward ROI.

    Custom exit reason max length is 64. Exceeding characters will be removed.

    :param pair: Pair that's currently analyzed
    :param trade: trade object.
    :param current_time: datetime object, containing the current datetime
    :param current_rate: Rate, calculated based on pricing settings in exit_pricing.
    :param current_profit: Current profit (as ratio), calculated based on current_rate.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return: To execute sell, return a string with custom exit reason or True. Otherwise return
    None or False.
    """
    return None

def confirm_trade_entry(
    self,
    pair: str,
    order_type: str,
    amount: float,
    rate: float,
    time_in_force: str,
    current_time: datetime,
    entry_tag: str | None,
    side: str,
    **kwargs,
) -> bool:
    """
    Called right before placing a entry order.
    Timing for this function is critical, so avoid doing heavy computations or
    network requests in this method.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, returns True (always confirming).

    :param pair: Pair that's about to be bought/shorted.
    :param order_type: Order type (as configured in order_types). usually limit or market.
    :param amount: Amount in target (base) currency that's going to be traded.
    :param rate: Rate that's going to be used when using limit orders
                 or current rate for market orders.
    :param time_in_force: Time in force. Defaults to GTC (Good-til-cancelled).
    :param current_time: datetime object, containing the current datetime
    :param entry_tag: Optional entry_tag (buy_tag) if provided with the buy signal.
    :param side: 'long' or 'short' - indicating the direction of the proposed trade
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return bool: When True is returned, then the buy-order is placed on the exchange.
        False aborts the process
    """
    return True

def confirm_trade_exit(
    self,
    pair: str,
    trade: Trade,
    order_type: str,
    amount: float,
    rate: float,
    time_in_force: str,
    exit_reason: str,
    current_time: datetime,
    **kwargs,
) -> bool:
    """
    Called right before placing a regular exit order.
    Timing for this function is critical, so avoid doing heavy computations or
    network requests in this method.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, returns True (always confirming).

    :param pair: Pair for trade that's about to be exited.
    :param trade: trade object.
    :param order_type: Order type (as configured in order_types). usually limit or market.
    :param amount: Amount in base currency.
    :param rate: Rate that's going to be used when using limit orders
                 or current rate for market orders.
    :param time_in_force: Time in force. Defaults to GTC (Good-til-cancelled).
    :param exit_reason: Exit reason.
        Can be any of ['roi', 'stop_loss', 'stoploss_on_exchange', 'trailing_stop_loss',
                        'exit_signal', 'force_exit', 'emergency_exit']
    :param current_time: datetime object, containing the current datetime
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return bool: When True, then the exit-order is placed on the exchange.
        False aborts the process
    """
    return True

def check_entry_timeout(
    self, pair: str, trade: Trade, order: Order, current_time: datetime, **kwargs
) -> bool:
    """
    Check entry timeout function callback.
    This method can be used to override the entry-timeout.
    It is called whenever a limit entry order has been created,
    and is not yet fully filled.
    Configuration options in `unfilledtimeout` will be verified before this,
    so ensure to set these timeouts high enough.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, this simply returns False.
    :param pair: Pair the trade is for
    :param trade: Trade object.
    :param order: Order object.
    :param current_time: datetime object, containing the current datetime
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return bool: When True is returned, then the entry order is cancelled.
    """
    return False

def check_exit_timeout(
    self, pair: str, trade: Trade, order: Order, current_time: datetime, **kwargs
) -> bool:
    """
    Check exit timeout function callback.
    This method can be used to override the exit-timeout.
    It is called whenever a limit exit order has been created,
    and is not yet fully filled.
    Configuration options in `unfilledtimeout` will be verified before this,
    so ensure to set these timeouts high enough.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, this simply returns False.
    :param pair: Pair the trade is for
    :param trade: Trade object.
    :param order: Order object.
    :param current_time: datetime object, containing the current datetime
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return bool: When True is returned, then the exit-order is cancelled.
    """
    return False

def adjust_trade_position(
    self,
    trade: Trade,
    current_time: datetime,
    current_rate: float,
    current_profit: float,
    min_stake: float | None,
    max_stake: float,
    current_entry_rate: float,
    current_exit_rate: float,
    current_entry_profit: float,
    current_exit_profit: float,
    **kwargs,
) -> float | None | tuple[float | None, str | None]:
    """
    Custom trade adjustment logic, returning the stake amount that a trade should be
    increased or decreased.
    This means extra entry or exit orders with additional fees.
    Only called when `position_adjustment_enable` is set to True.

    For full documentation please go to https://www.freqtrade.io/en/latest/strategy-advanced/

    When not implemented by a strategy, returns None

    :param trade: trade object.
    :param current_time: datetime object, containing the current datetime
    :param current_rate: Current entry rate (same as current_entry_profit)
    :param current_profit: Current profit (as ratio), calculated based on current_rate
                            (same as current_entry_profit).
    :param min_stake: Minimal stake size allowed by exchange (for both entries and exits)
    :param max_stake: Maximum stake allowed (either through balance, or by exchange limits).
    :param current_entry_rate: Current rate using entry pricing.
    :param current_exit_rate: Current rate using exit pricing.
    :param current_entry_profit: Current profit using entry pricing.
    :param current_exit_profit: Current profit using exit pricing.
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return float: Stake amount to adjust your trade,
                   Positive values to increase position, Negative values to decrease position.
                   Return None for no action.
    """
    return None

def leverage(
    self,
    pair: str,
    current_time: datetime,
    current_rate: float,
    proposed_leverage: float,
    max_leverage: float,
    entry_tag: str | None,
    side: str,
    **kwargs,
) -> float:
    """
    Customize leverage for each new trade. This method is only called in futures mode.

    :param pair: Pair that's currently analyzed
    :param current_time: datetime object, containing the current datetime
    :param current_rate: Rate, calculated based on pricing settings in exit_pricing.
    :param proposed_leverage: A leverage proposed by the bot.
    :param max_leverage: Max leverage allowed on this pair
    :param entry_tag: Optional entry_tag (buy_tag) if provided with the buy signal.
    :param side: 'long' or 'short' - indicating the direction of the proposed trade
    :return: A leverage amount, which is between 1.0 and max_leverage.
    """
    return 1.0


def order_filled(
    self, pair: str, trade: Trade, order: Order, current_time: datetime, **kwargs
) -> None:
    """
    Called right after an order fills.
    Will be called for all order types (entry, exit, stoploss, position adjustment).
    :param pair: Pair for trade
    :param trade: trade object.
    :param order: Order object.
    :param current_time: datetime object, containing the current datetime
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    """
    pass

def plot_annotations(
    self, pair: str, start_date: datetime, end_date: datetime, dataframe: DataFrame, **kwargs
) -> list[AnnotationType]:
    """
    Retrieve area annotations for a chart.
    Must be returned as array, with type, label, color, start, end, y_start, y_end.
    All settings except for type are optional - though it usually makes sense to include either
    "start and end" or "y_start and y_end" for either horizontal or vertical plots
    (or all 4 for boxes).
    :param pair: Pair that's currently analyzed
    :param start_date: Start date of the chart data being requested
    :param end_date: End date of the chart data being requested
    :param dataframe: DataFrame with the analyzed data for the chart
    :param **kwargs: Ensure to keep this here so updates to this won't break your strategy.
    :return: List of AnnotationType objects
    """
    return []
