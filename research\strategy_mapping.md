# Strategy - Research Mapping

This document maps implemented strategies or strategy ideas to the originating research papers and their key takeaways.

| Strategy Component / Idea | Source Paper(s) / URL(s) | Key Findings / Technique Used | Implementation Notes in Freqtrade |
|---------------------------|--------------------------|-------------------------------|-----------------------------------|
| **General Technical Analysis & Candlesticks** | dotnettutorials.net | **Candlestick Basics:** Anatomy (Open, High, Low, Close, Body, Wicks/Shadows, Range). Body represents the range between open and close; wicks show the highest/lowest prices reached. Green/White (Bullish): Close > Open. Red/Black (Bearish): Close < Open. <br> **Market Psychology:** Candlesticks reflect buyer/seller pressure. Long wicks indicate rejection of certain price levels. <br> **Volume Confirmation:** High volume on a significant candle pattern increases its reliability. Low volume might indicate weak conviction. <br> **Key Candlestick Patterns:** <br> - *Doji:* Indecision (Open ≈ Close). Potential reversal. <br> - *Hammer/Hanging Man:* Small body, long lower/upper wick. Potential bullish/bearish reversal after downtrend/uptrend respectively, especially at S/R. <br> - *Shooting Star/Inverted Hammer:* Similar to Hammer but inverted. Potential bearish/bullish reversal. <br> - *Engulfing (Bullish/Bearish):* Second candle's body completely engulfs the first. Strong reversal signal, especially with volume. <br> - *Morning/Evening Star:* Three-candle reversal patterns. <br> **Context is Key:** Combine with Support/Resistance (S/R) levels, trendlines, moving averages, and overall market structure. A pattern's location significantly impacts its validity. | Identify key candlestick patterns (Doji, Hammer, Engulfing, etc.). Use volume as a confirmation signal. Integrate pattern recognition with existing S/R and trend logic. Consider the context (trend direction, S/R zones) when evaluating patterns. |
| **Price Action Analysis** | dotnettutorials.net | **Core Concepts:** Trading based on raw price movement without heavy reliance on lagging indicators. Focus on identifying patterns, S/R levels, trendlines, and market structure. <br> **Swing Highs/Lows:** Key turning points in price. Higher highs (HH) and higher lows (HL) define an uptrend. Lower highs (LH) and lower lows (LL) define a downtrend. <br> **Thrust & Pullback:** <br> - *Thrust (Impulse Move):* Strong, decisive price movement in the direction of the trend. <br> - *Pullback (Correction/Retracement):* Temporary counter-trend movement before the primary trend resumes. Shallow pullbacks in strong trends; deeper pullbacks in weaker trends. <br> **Measuring Moves:** Projecting potential price targets based on the length of previous impulse moves. <br> **Market Structure Phases (Wyckoff-like):** <br> - *Accumulation:* Sideways movement after a downtrend, where smart money buys. <br> - *Markup (Uptrend):* Price breaks out of accumulation, making HHs and HLs. <br> - *Distribution:* Sideways movement after an uptrend, where smart money sells. <br> - *Markdown (Downtrend):* Price breaks out of distribution, making LHs and LLs. <br> **Volume in Price Action:** Volume should confirm price moves. High volume on breakouts, low volume on pullbacks (in a healthy trend). Divergence between price and volume can signal weakness. | Detect swing points (highs and lows) to determine short-term and long-term trends. Analyze the strength of trends based on the characteristics of thrusts (impulse waves) and pullbacks (corrective waves). Identify potential market phases (accumulation, markup, distribution, markdown). Use volume to confirm price action signals. |
| **Day Trading Strategies** | dotnettutorials.net | **VWAP (Volume Weighted Average Price):** Dynamic S/R, often used for intraday mean reversion or trend confirmation. Trades above VWAP may be bullish; below may be bearish. <br> **Gap Trading:** Exploiting price gaps between a previous day's close and the current day's open. <br> - *Full Gap Fill:* Price returns to close the entire gap. <br> - *Partial Gap Fill:* Price retraces only a portion of the gap. <br> - *Gap and Go (Fade):* Price continues in the direction of the gap without filling. <br> **Open High/Low (OHOL) Breakouts:** Trading breakouts above the opening high or below the opening low, often within a specific initial period (e.g., first 15-30 mins). <br> **Pin Bar Reversals:** Pin bars (long wick, small body) at key S/R levels or after a strong move, signaling potential reversal. <br> **Pullback Entries:** Waiting for a price correction (pullback) towards a dynamic S/R (e.g., moving average) or static S/R in an established trend, then entering in the trend's direction. <br> **Breakout Trading:** Entering when price breaks out of a consolidation pattern (e.g., range, triangle) or a significant S/R level, ideally with increased volume. | Implement VWAP calculations and use it as a dynamic S/R level. Detect price gaps between trading sessions (if applicable to crypto 24/7 markets, this might refer to significant overnight moves). Identify Opening Range Breakouts (ORB). Recognize Pin Bar patterns at key S/R levels or MAs. Develop logic for entering on pullbacks to MAs or S/R zones within a trend. Build systems to detect breakouts from consolidation patterns (e.g., flags, pennants, ranges) with volume confirmation. |
| **Risk Management** | dotnettutorials.net | **Position Sizing:** Determining the appropriate amount of capital to risk on a single trade, often a fixed percentage of total trading capital (e.g., 1-2%). <br> **Stop-Loss Orders:** Predetermined price level at which a losing trade is exited to limit losses. Essential for capital preservation. Types: static, trailing. <br> **Risk/Reward Ratio (R/R):** Comparing the potential profit of a trade to its potential loss. Aim for R/R > 1 (e.g., 2:1, 3:1). <br> **Diversification:** Spreading capital across different assets or strategies to reduce overall risk (less applicable to a single-strategy bot but relevant for a portfolio). <br> **Psychological Aspects:** Emotional discipline, avoiding overtrading, revenge trading, and FOMO. Importance of a trading plan and sticking to it. | Enforce strict stop-loss placement for every trade based on volatility (e.g., ATR) or price structure (e.g., below swing low for long). Calculate position size dynamically based on a fixed percentage of equity risk per trade and stop-loss distance. Aim for strategies with a positive expected R/R ratio. Log trades and review performance to manage psychological biases. |
| **Volume Price Action Analysis** | dotnettutorials.net | **Volume Confirming Price:** <br> - *Trend Confirmation:* Rising volume in an uptrend, falling volume in pullbacks. Rising volume in a downtrend, falling volume in rallies. <br> - *Breakout Confirmation:* High volume on a breakout from a key level or pattern. <br> - *Exhaustion Volume (Climax):* Extremely high volume after a prolonged move, potentially signaling a reversal (e.g., Selling Climax, Buying Climax). <br> **Wyckoff Concepts (simplified):** <br> - *Accumulation/Distribution:* Smart money activity seen in volume signatures during sideways ranges. <br> - *Effort vs. Result:* Price movement (result) should be proportional to volume (effort). Divergence can signal weakness. <br> **VSA (Volume Spread Analysis):** Analyzing the relationship between price spread (candle range), closing price, and volume to infer smart money intentions. <br> - *Stopping Volume:* High volume with a narrow spread candle, often stopping a downtrend. <br> - *No Demand/No Supply:* Low volume on attempts to rally/decline, indicating lack of interest. <br> - *Shakeout/Spring/Upthrust:* Price briefly breaks a key level on volume to trap traders, then reverses. | Integrate volume data into all signal generation and confirmation. Look for divergences between price trends and volume trends. Identify key VSA patterns: Stopping Volume, No Demand/Supply bars, Upthrusts, Springs, indications of Absorption. Use volume to confirm breakouts and trend strength. |
| **Chart Patterns** | dotnettutorials.net | **Continuation Patterns:** Signal that an ongoing trend is likely to continue after a pause. <br> - *Flags & Pennants:* Short-term consolidations after a sharp move. <br> - *Triangles (Ascending, Descending, Symmetrical):* Converging trendlines. Breakout direction varies. <br> - *Cup & Handle:* Bullish continuation pattern with a 'U' shaped cup and a smaller pullback (handle). <br> **Reversal Patterns:** Signal that a trend may be about to change direction. <br> - *Head & Shoulders (H&S) / Inverse H&S:* Three peaks/troughs, with the middle one being the highest/lowest. <br> - *Double Top/Bottom:* Two failed attempts to break a S/R level. <br> - *Wedges (Rising/Falling):* Converging trendlines slanted against the prevailing trend. Often signal reversal. <br> **Volume Confirmation:** Volume typically diminishes during pattern formation and increases on breakout. | Develop algorithms to detect common chart patterns (Flags, Triangles, H&S, Double Tops/Bottoms, Wedges). Use pattern completion (e.g., breakout from a flag, neckline break in H&S) as entry or exit signals. Confirm breakouts with increased volume. |
| **CPR, Pivot Points, VWAP System** | dotnettutorials.net | **Central Pivot Range (CPR):** Calculated from previous period's High, Low, Close. Acts as a key daily S/R zone. Narrow CPR suggests trending day; wide CPR suggests range-bound day. Price interaction with CPR levels (acceptance/rejection) provides clues. <br> **Pivot Points:** Standard daily/weekly/monthly pivot points (PP, S1, R1, S2, R2, etc.) act as potential S/R levels. <br> **VWAP (Volume Weighted Average Price):** Dynamic S/R. In uptrends, price tends to stay above VWAP; in downtrends, below. Can be used for mean reversion entries if price deviates significantly and then returns to VWAP, or as a trend filter. | Calculate daily and weekly CPR levels (TC, Pivot, BC). Use these as key S/R zones. Calculate standard Pivot Points. Integrate VWAP as a dynamic S/R and trend filter. Consider strategies based on CPR width and price interaction with CPR/Pivot/VWAP levels. |
| **Specific Trading Strategies (Darvas, VCP, Volume Spike, Fibonacci)** | dotnettutorials.net | **Darvas Box:** Momentum strategy. Buy when price breaks out of a "box" (defined by recent highs and lows) on high volume, typically in strong uptrends. <br> **Volatility Contraction Pattern (VCP) / Mark Minervini:** Price consolidates in a series of progressively tighter ranges (contractions) before a powerful breakout, usually in an uptrend. Volume typically dries up during contractions and expands on breakout. <br> **Volume Spike:** A sudden, unusually high volume bar. Can signal initiation of a new move, climax of an old move, or strong S/R test. Context is crucial. <br> **Fibonacci Retracements & Extensions:** <br> - *Retracements (e.g., 0.382, 0.50, 0.618):* Potential S/R levels where price might reverse during a pullback. <br> - *Extensions (e.g., 1.272, 1.618, 2.618):* Potential price targets for a move. <br> **Confluence:** Combining Fibonacci levels with other S/R indicators increases their significance. | Implement logic for Darvas Box formation and breakout. Develop VCP detection algorithms looking for tightening price ranges and volume patterns. Identify significant volume spikes and analyze their context (breakout, reversal, S/R test). Incorporate Fibonacci retracement levels as potential entry points during pullbacks and extension levels as potential profit targets. |
| **Smart Money Concepts (SMC)** | dotnettutorials.net | **Order Blocks (OB):** The last down-close candle before a strong up-move (bullish OB) or the last up-close candle before a strong down-move (bearish OB) that leads to a Break of Structure (BoS) or imbalance. Price is expected to return to mitigate these OBs. <br> **Market Structure:** <br> - *Break of Structure (BoS):* Price breaks a previous swing high in an uptrend or swing low in a downtrend, confirming trend continuation. <br> - *Change of Character (CHoCH):* Price breaks the last pullback's swing point against the prevailing trend, signaling a potential trend reversal (e.g., breaking a HL in an uptrend). <br> **Breaker Blocks:** A failed Order Block that is violated. When price returns to a violated OB (e.g., a bullish OB that failed and was broken downwards), it can act as resistance (bearish breaker). <br> **Liquidity:** Areas where stop-loss orders are likely to accumulate (e.g., above/below old highs/lows, trendlines). Smart money often engineers "liquidity grabs" or "stop hunts" by pushing price to these levels to trigger stops and fill their large orders before reversing. <br> - *Inducement:* Price creating a seemingly obvious S/R level to entice retail traders before a liquidity grab. <br> **Imbalance / Fair Value Gap (FVG):** A large price move leaving a gap between the wicks of three consecutive candles, indicating inefficient price delivery. Price often returns to fill these gaps. <br> **Supply/Demand (S/D) Zones:** Areas of strong buying/selling pressure. S/D Flips occur when a demand zone is broken and becomes supply, or vice-versa. | Identify high-probability Order Blocks (considering imbalance, BoS, liquidity grabs). Detect BoS for trend continuation entries and CHoCH for potential reversal setups. Find Breaker Blocks for entry opportunities. Look for liquidity sweeps (stop hunts) above/below key swing points or equal highs/lows as potential entry triggers. Identify Fair Value Gaps (Imbalances) as potential targets or areas for price to retrace to. |
| **Pullback Trading** | dotnettutorials.net | **Definition:** A temporary price movement against the prevailing trend, offering an opportunity to enter in the direction of the trend at a better price. <br> **Types:** <br> - *Time Correction:* Price moves sideways with low volatility (shallow pullback), common in strong trends. <br> - *Price Correction:* Price retraces a portion of the previous impulse move (deeper pullback), common in healthy or weaker trends. <br> **Characteristics of Pullbacks:** <br> - *Weak Pullback (Trend Continuation Likely):* Small corrective candles, low volume, mixed green/red candles, shallow retracement. <br> - *Strong Pullback (Potential Range/Reversal):* Series of strong counter-trend candles, significant volume, deep retracement. <br> **Where Pullbacks End:** Previous S/R levels, dynamic S/R (moving averages), Fibonacci retracement levels, trendlines. <br> **Entry Techniques:** Wait for confirmation (e.g., reversal candlestick pattern at the end of pullback, break of a mini-trendline within the pullback). Avoid entering too early. Conservative vs. Aggressive entries. <br> **Mistakes:** Trading pullbacks in a ranging market, misinterpreting a reversal as a pullback, entering without confirmation. | Differentiate between pullbacks (trend-following) and reversals. Identify the end of a pullback using S/R levels, MAs, or Fibonacci. Wait for clear entry signals (e.g., bullish engulfing at support after a pullback in an uptrend). Adjust entry/stop strategy based on pullback strength and depth. |
| **Swing Trading & Market Sentiment** | dotnettutorials.net | **General Market Analysis:** Understanding the broader market trend and conditions (e.g., is it a bull market, bear market, or range-bound?). This provides context for individual trades. <br> **Market Sentiment Analysis:** Gauging the overall mood of market participants (e.g., fear/greed index, put/call ratio, news sentiment). Extreme sentiment can be a contrarian indicator. <br> **Sector Rotation:** Money flowing between different market sectors. Identifying leading/lagging sectors can provide trading opportunities (more relevant for stocks). <br> **Momentum Strategies:** Trading in the direction of strong, established price moves, often identified by indicators like ROC, MACD, or simply by observing sustained price increases/decreases with strong volume. Buy high, sell higher or sell low, buy lower. | Consider the broader market context/trend (e.g., from higher timeframes) as a filter for strategy signals. Potentially use sentiment indicators (if available for crypto and reliable) as an additional filter or for contrarian setups at extremes. Focus on assets showing strong relative momentum compared to the broader market or other similar assets. |
|                           |                          |                               |                                   | 