{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 12000, "temperature": 0.2}, "research": {"provider": "openrouter", "modelId": "google/gemini-2.5-flash-preview-05-20:thinking", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.5-flash-preview-05-20", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}