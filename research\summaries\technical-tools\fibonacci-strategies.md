# Fibonacci Trading Strategies

Fibonacci retracements and extensions are powerful tools for identifying entry points, profit targets, and key support/resistance levels. This guide covers both basic Fibonacci trading and advanced confluence strategies.

## Table of Contents

- [Introduction to Fibonacci Trading](#introduction-to-fibonacci-trading)
- [<PERSON><PERSON><PERSON><PERSON> vs Other Indicators](#fibonacci-vs-other-indicators)
- [Fibonacci Retracement](#fibonacci-retracement)
- [Fibonacci Extension](#fibonacci-extension)
- [Entry Strategies](#entry-strategies)
- [Confluence Factor Trading](#confluence-factor-trading)
- [Advanced Confluence Strategies](#advanced-confluence-strategies)
- [Key Trading Rules](#key-trading-rules)

## Introduction to Fibonacci Trading

Fibonacci trading is based on the mathematical sequence discovered by <PERSON>, where each number is the sum of the two preceding ones. In trading, these ratios (23.6%, 38.2%, 50%, 61.8%, 78.6%, 100%) are used to identify potential support and resistance levels.

### Why Fibonacci Works

**Multiple Trader Participation:**

- Many traders put alerts at different Fibonacci retracement levels
- Price action traders look for buy-on-dips in established uptrends
- Indicator traders look for entries at trendlines, moving averages, etc.
- All these confluence factors increase probability of success

## Fibonacci vs Other Indicators

### Moving Average System Problems

- **Late Entry** - Due to rapid price movement
- **Late Exit Signals** - Missing optimal exit points
- **Deep Retracements** - Force exit regardless of eventual direction

### Fibonacci System Advantages

- **Earlier Entry and Exit Decisions** - More precise timing
- **Better Profit Capture** - Using extension levels for targets
- **More Precise Timing** - Retracement levels provide specific entry zones

## Fibonacci Retracement

### Core Principle

After starting a new trend, price will retrace before continuing in trend direction. Retracement levels predict potential support and resistance levels and provide opportunities to enter trades in trend direction.

### Key Retracement Levels

- **23.6%** - Shallow retracement
- **38.2%** - Moderate retracement
- **50%** - Psychological level (not technically Fibonacci)
- **61.8%** - Golden retracement level (most important)
- **78.6%** - Deep retracement
- **100%** - Complete retracement

**Important Range:** 50% to 61.8% is the most watched zone.

### How to Draw Fibonacci Retracement

1. Find recent important Swing High and Swing Low
2. In **uptrend**: Drag from swing LOW (starting point) to swing HIGH (ending point)
3. In **downtrend**: Drag from swing HIGH (starting point) to swing LOW (ending point)
4. Use with confluence factors for entry confirmation

## Fibonacci Extension

### Purpose

- Based on three points (A, B, C)
- Used for profit targets and exit strategies
- Predicts how far market may move after retracement completion

### How to Draw (Example for Downtrend)

1. **Identify market direction** (downtrend)
2. **Identify impulse swing** (A and B points) and retracement end (point C)
3. **Attach Fibonacci extension tool** on swing high, drag to swing low
4. **Drag back to retracement end** (point C)

### Common Extension Levels

- **61.8%** - First target
- **100%** - Equal move target
- **161.8%** - Golden extension
- **200%** - Double move target
- **261.8%** - Extended target

### Profit Booking Strategy

1. **Divide position into three parts**
2. **Close first part** at 100% extension
3. **Close second part** at 161.8% extension
4. **Let third part run** with manual close using technical triggers

## Entry Strategies

### Important Notes

- Fibonacci retracements are target areas, not entry signals
- Price doesn't need to touch retracement levels exactly
- Always use confluence factors for confirmation
- Works best in trending markets

### Three Types of Entry

#### 1. Aggressive Entry

- **Risk Level:** Highest risk, smallest loss, high reward
- **Method:** Enter when price nears/reaches 61.8% retracement
- **Steps:**
  1. Identify trend and Fibonacci retracement level
  2. Look for confluence factor
  3. Wait for reversal candlestick pattern
  4. Draw extension lines for targets
  5. Put stop loss beyond recent high/Fibonacci level

#### 2. Conservative Entry

- **Risk Level:** Moderate risk, good risk/reward ratio
- **Method:** Wait for confirmation after retracement
- **Steps:**
  1. Identify trend and Fibonacci retracement level
  2. Look for confluence factor
  3. Wait for break of minor support (bearish trade)
  4. Draw extension lines for targets
  5. Put stop loss beyond recent high/Fibonacci level

#### 3. Safe Entry

- **Risk Level:** Lowest risk, smallest profit potential
- **Method:** Buy breakout after price contraction
- **Steps:**
  1. Identify trend and Fibonacci retracement level
  2. Look for price contraction before breakout
  3. Wait for break of support
  4. Draw extension lines for targets
  5. Put stop loss beyond contraction high

## Confluence Factor Trading

### What is Area of Confluence?

**Definition:** Confluence means finding multiple reasons for taking a trade. "Area of Confluence" refers to a price zone where several technical analysis tools converge, suggesting the same possible price movement.

**Benefits:**
- Stronger levels of support/resistance due to multiple parameter interaction
- Many traders taking entry at same zone creates momentum in your favor
- Higher probability of success

### Key Confluence Factors

1. **Fibonacci retracement**
2. **Horizontal support and resistance**
3. **Supply and demand zones**
4. **Trendlines**
5. **Moving averages**
6. **Chart patterns**

**Rule:** More confluence = stronger signal

### Basic Requirements for Confluence Trading

**Essential Elements:**

1. **Trend trading** - Work with the trend
2. **Clear impulse move** - Strong directional movement
3. **First pullback** in newly established trend

## Advanced Confluence Strategies

### Fibonacci + Trendline Confluence

**Strategy Steps:**

1. **Identify the trend** - Higher highs/lows (uptrend) or lower highs/lows (downtrend)
2. **Draw trendline** - Connect two or more lows (uptrend) or highs (downtrend)
3. **Use Fibonacci retracement** - Find confluence with trendline
4. **Entry/exit points** - Buy near trendline support (uptrend)
5. **Stop loss** - Below trendline (uptrend) or above (downtrend)

### Fibonacci + Support/Resistance Confluence

**Strategy Steps:**

1. **Identify clear trend** - Look for impulse move beyond support/resistance
2. **Identify key levels** - Previous support/resistance or supply/demand zones
3. **Use Fibonacci tool** - Find confluence between S/R levels and Fibonacci retracement
4. **Entry points** - Buy near support confluence in uptrend
5. **Stop loss** - Above entry signal high in downtrend

### Fibonacci + Moving Average Confluence

**Strategy Steps:**

1. **Plot moving averages** - Identify trend direction and support/resistance areas
2. **Use Fibonacci retracement** - Plot levels between high and low points
3. **Look for confluence** - Key moving average aligning with key Fibonacci level (61.8% or 50%)
4. **Trade decision** - Enter long at confluence of support (e.g., 61.8% Fib + 50 MA)

### Multiple Confluence Factor Strategy

**Advanced Approach:**

- Combines Fibonacci retracement levels with multiple indicators
- Uses moving averages, trendlines, support/resistance, supply/demand zones, and chart patterns
- Provides more complete market picture
- Potentially more precise trading signals

**Example Confluence Setup:**
- Falling wedge pattern
- 100 simple moving average
- 61.8% Fibonacci retracement
- Double bottom reversal pattern

## Key Trading Rules

### Entry Rules

- Use Fibonacci with confluence factors (trend lines, pivot points, dynamic support/resistance)
- Price can be valid near retracement levels without exact touch
- Choose entry type based on risk tolerance and reward expectations
- Always draw extension lines for profit targets

### Risk Management

- Proper stop-loss placement is crucial
- First pullback in newly established trend offers best opportunities
- Clear impulse moves are essential for valid Fibonacci setups
- More confluence factors = higher probability of success

### Market Structure Requirements

- **Trend trading** - Work with established trends
- **Clear impulse move** - Strong directional movement required
- **First pullback** - Best opportunities in newly established trends

## Related Concepts

- [Trendline Trading](trendline-trading.md) - Combining trendlines with Fibonacci
- [Support and Resistance](../foundations/support-resistance.md) - Key levels for confluence
- [Multiple Timeframe Analysis](multiple-timeframe-analysis.md) - Using Fibonacci across timeframes

---

*For practical applications, see [Pullback Trading](../specialized/pullback-trading.md) strategies*
